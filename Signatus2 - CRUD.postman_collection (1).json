{"info": {"_postman_id": "e7df5876-2ae3-4f3c-88cc-01dcfa517219", "name": "Signatus2 - CRUD", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "13311497"}, "item": [{"name": "create envelope 3x Document", "item": [{"name": "1. login oauth", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '200') {\r", "    pm.test('login - pozitivny scenar', function () {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    var jsonData = pm.response.json();\r", "    pm.environment.set(\"refresh_token\", jsonData.refresh_token);\r", "    pm.environment.set(\"access_token\", jsonData.access_token);\r", "    pm.environment.set(\"id_token\", jsonData.id_token);\r", "\r", "    pm.test(\"Access token is not empty\", function () {\r", "        pm.expect(jsonData.access_token).to.exist.and.to.not.be.empty;\r", "    });\r", "\r", "    pm.test(\"Refresh token is not empty\", function () {\r", "        pm.expect(jsonData.refresh_token).to.exist.and.to.not.be.empty;\r", "    });\r", "\r", "    pm.test(\"Token type is present and has a valid value\", function () {\r", "        pm.expect(jsonData).to.have.property('token_type');\r", "        pm.expect(jsonData.token_type).to.be.a('string').and.to.not.be.empty;\r", "    });\r", "}else {\r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    pm.execution.setNextRequest(null);   \r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN", "type": "string"}, {"key": "username", "value": "signatus", "type": "string"}, {"key": "saveHelperData", "value": true, "type": "boolean"}, {"key": "showPassword", "value": false, "type": "boolean"}]}, "method": "POST", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password", "type": "text"}, {"key": "username", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "password", "value": "S#DgfsH64", "type": "text"}, {"key": "client_id", "value": "", "type": "text", "disabled": true}, {"key": "scope", "value": "openid", "type": "text"}]}, "url": {"raw": "https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token", "protocol": "https", "host": ["<PERSON><PERSON><PERSON><PERSON>", "ana", "sk"], "path": ["realms", "Signatus", "protocol", "openid-connect", "token"]}}, "response": []}, {"name": "info envelope {{envelopeId}}", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '401' || pm.response.code == '409') {    \r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else if ( pm.response.code == '404') {\r", "    let envelopeId = pm.environment.get(\"envelopeId\"); \r", "    pm.test(('ERROR get envelope not exist id: '+envelopeId), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else {\r", "    var json = pm.response.json();\r", "    pm.test('info envelope - stav final', function () {\r", "        pm.expect(json.state).to.eql(\"final\");\r", "    })\r", "\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["        const moment = require(\"moment\");\r", "        let startMoment = moment();\r", "        setTimeout(function() {\r", "            let diffDuration = startMoment.diff(moment());\r", "            pm.test(\"pauseForStateTransition() - paused for \" + diffDuration.milliseconds() + \" milliseconds\");\r", "        }, 500);\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://ds-sts.ana.sk/workflow/api/envelope/{{envelopeId}}", "protocol": "https", "host": ["ds-sts", "ana", "sk"], "path": ["workflow", "api", "envelope", "{{envelopeId}}"]}}, "response": []}]}]}