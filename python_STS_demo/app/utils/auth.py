from functools import wraps
from flask import request, jsonify, current_app

def require_api_key(f):
    """Dekorátor pre overenie API kľúča"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('x-api-key')
        
        if not api_key:
            return jsonify({"error": "API kľúč je povinný"}), 401
        
        if api_key not in current_app.config['API_KEYS']:
            return jsonify({"error": "Neplatný API kľúč"}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function
