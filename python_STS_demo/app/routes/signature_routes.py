from flask import Blueprint, request, jsonify, current_app
from app.models.signature import Signature
from app.utils.auth import require_api_key

signature_bp = Blueprint('signature', __name__)

@signature_bp.route('/signature', methods=['POST'])
@require_api_key
def create_signature():
    """Vytvorí nový podpis pre dokument"""
    try:
        data = request.json
        
        if not data:
            return jsonify({"error": "Chýbajúce dáta"}), 400
        
        # Kontrola povinných polí
        if 'documentId' not in data:
            return jsonify({"error": "Chýbajúce ID dokumentu"}), 400
        
        document_id = data['documentId']
        
        # Kontrola, či dokument existuje
        document = current_app.config['DOCUMENTS_STORAGE'].get(document_id)
        if not document:
            return jsonify({"error": "Dokument nebol nájdený"}), 404
        
        # Vytvorenie nového podpisu
        signature = Signature(
            document_id=document_id,
            signer_email=data.get('signerEmail'),
            signer_name=data.get('signerName')
        )
        
        # Pridanie podpisu k dokumentu
        document.add_signature(signature)
        
        # Uloženie podpisu do úložiska
        current_app.config['SIGNATURES_STORAGE'][signature.signature_id] = signature
        
        # Vrátenie odpovede
        return jsonify(signature.to_dict()), 201
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@signature_bp.route('/signature/<signature_id>', methods=['GET'])
@require_api_key
def get_signature(signature_id):
    """Získa informácie o podpise"""
    try:
        # Získanie podpisu z úložiska
        signature = current_app.config['SIGNATURES_STORAGE'].get(signature_id)
        
        if not signature:
            return jsonify({"error": "Podpis nebol nájdený"}), 404
        
        # Vrátenie odpovede
        return jsonify(signature.to_dict()), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@signature_bp.route('/signature/<signature_id>/sign', methods=['POST'])
@require_api_key
def sign_document(signature_id):
    """Podpíše dokument"""
    try:
        # Získanie podpisu z úložiska
        signature = current_app.config['SIGNATURES_STORAGE'].get(signature_id)
        
        if not signature:
            return jsonify({"error": "Podpis nebol nájdený"}), 404
        
        # Podpísanie dokumentu
        signature.sign()
        
        # Aktualizácia dokumentu
        document = current_app.config['DOCUMENTS_STORAGE'].get(signature.document_id)
        if document:
            document.add_signature(signature)
        
        # Vrátenie odpovede
        return jsonify(signature.to_dict()), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@signature_bp.route('/signature/<signature_id>/reject', methods=['POST'])
@require_api_key
def reject_signature(signature_id):
    """Odmietne podpis dokumentu"""
    try:
        # Získanie podpisu z úložiska
        signature = current_app.config['SIGNATURES_STORAGE'].get(signature_id)
        
        if not signature:
            return jsonify({"error": "Podpis nebol nájdený"}), 404
        
        # Odmietnutie podpisu
        signature.reject()
        
        # Aktualizácia dokumentu
        document = current_app.config['DOCUMENTS_STORAGE'].get(signature.document_id)
        if document:
            document.add_signature(signature)
        
        # Vrátenie odpovede
        return jsonify(signature.to_dict()), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500
