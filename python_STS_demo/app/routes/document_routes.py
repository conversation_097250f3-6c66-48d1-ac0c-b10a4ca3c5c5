from flask import Blueprint, request, jsonify, current_app
from app.models.document import Document
from app.utils.auth import require_api_key
import base64
import json

document_bp = Blueprint('document', __name__)

@document_bp.route('/document', methods=['POST'])
@require_api_key
def create_document():
    """Vytvorí nový dokument na podpísanie"""
    try:
        data = request.json
        
        if not data:
            return jsonify({"error": "Chýbajúce dáta"}), 400
        
        # Kontrola povinných polí
        if 'filedata' not in data or 'content' not in data['filedata']:
            return jsonify({"error": "Chýbajúci obsah dokumentu"}), 400
        
        # Dekódovanie obsahu dokumentu z base64
        content = data['filedata']['content']
        
        # Vytvorenie nového dokumentu
        document = Document(
            content=content,
            result_url=data.get('resulturl'),
            state=data.get('state', 'tosign'),
            expiration_time=data.get('expirationTime'),
            rules=data.get('rules', '')
        )
        
        # Uloženie dokumentu do úložiska
        current_app.config['DOCUMENTS_STORAGE'][document.document_id] = document
        
        # Vrátenie odpovede
        return jsonify({
            "documentId": document.document_id,
            "result": document.signature_request_id
        }), 201
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@document_bp.route('/document/<document_id>', methods=['GET'])
@require_api_key
def get_document(document_id):
    """Získa informácie o dokumente"""
    try:
        # Získanie dokumentu z úložiska
        document = current_app.config['DOCUMENTS_STORAGE'].get(document_id)
        
        if not document:
            return jsonify({"error": "Dokument nebol nájdený"}), 404
        
        # Vrátenie odpovede
        return jsonify(document.to_dict()), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@document_bp.route('/document/<document_id>', methods=['PUT'])
@require_api_key
def update_document(document_id):
    """Aktualizuje dokument"""
    try:
        data = request.json
        
        if not data:
            return jsonify({"error": "Chýbajúce dáta"}), 400
        
        # Získanie dokumentu z úložiska
        document = current_app.config['DOCUMENTS_STORAGE'].get(document_id)
        
        if not document:
            return jsonify({"error": "Dokument nebol nájdený"}), 404
        
        # Aktualizácia dokumentu
        if 'state' in data:
            document.update_state(data['state'])
        
        if 'resulturl' in data:
            document.result_url = data['resulturl']
        
        if 'expirationTime' in data:
            document.expiration_time = data['expirationTime']
        
        if 'rules' in data:
            document.rules = data['rules']
        
        # Uloženie aktualizovaného dokumentu
        current_app.config['DOCUMENTS_STORAGE'][document_id] = document
        
        # Vrátenie odpovede
        return jsonify(document.to_dict()), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@document_bp.route('/document/<document_id>', methods=['DELETE'])
@require_api_key
def delete_document(document_id):
    """Vymaže dokument"""
    try:
        # Kontrola, či dokument existuje
        if document_id not in current_app.config['DOCUMENTS_STORAGE']:
            return jsonify({"error": "Dokument nebol nájdený"}), 404
        
        # Vymazanie dokumentu z úložiska
        del current_app.config['DOCUMENTS_STORAGE'][document_id]
        
        # Vrátenie odpovede
        return jsonify({"message": "Dokument bol úspešne vymazaný"}), 200
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500
