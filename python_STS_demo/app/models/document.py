import uuid
import time
from datetime import datetime

class Document:
    """Model pre dokument v systéme STS"""
    
    def __init__(self, content, result_url=None, state="tosign", expiration_time=None, rules=None):
        self.document_id = str(uuid.uuid4())
        self.signature_request_id = str(uuid.uuid4())
        self.content = content
        self.result_url = result_url or "https://www.signatus.com"
        self.state = state
        self.expiration_time = expiration_time or int(time.time() * 1000) + 30 * 24 * 60 * 60 * 1000  # 30 dní
        self.rules = rules or ""
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.signatures = []
    
    def to_dict(self):
        """Konvertuje dokument na slovník"""
        return {
            "documentId": self.document_id,
            "result": self.signature_request_id,
            "resulturl": self.result_url,
            "state": self.state,
            "expirationTime": self.expiration_time,
            "rules": self.rules,
            "createdAt": self.created_at,
            "updatedAt": self.updated_at,
            "signatures": [signature.to_dict() for signature in self.signatures]
        }
    
    def add_signature(self, signature):
        """Pridá podpis k dokumentu"""
        self.signatures.append(signature)
        self.updated_at = datetime.now().isoformat()
        
        # Ak sú všetky podpisy dokončené, zmení stav dokumentu na "signed"
        if all(signature.state == "signed" for signature in self.signatures):
            self.state = "signed"
        
        return self
    
    def update_state(self, new_state):
        """Aktualizuje stav dokumentu"""
        self.state = new_state
        self.updated_at = datetime.now().isoformat()
        return self
