import uuid
from datetime import datetime

class Signature:
    """Model pre podpis v systéme STS"""
    
    def __init__(self, document_id, signer_email=None, signer_name=None):
        self.signature_id = str(uuid.uuid4())
        self.document_id = document_id
        self.signer_email = signer_email
        self.signer_name = signer_name
        self.state = "pending"  # pending, signed, rejected
        self.signed_at = None
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
    
    def to_dict(self):
        """Konvertuje podpis na slovník"""
        return {
            "signatureId": self.signature_id,
            "documentId": self.document_id,
            "signerEmail": self.signer_email,
            "signerName": self.signer_name,
            "state": self.state,
            "signedAt": self.signed_at,
            "createdAt": self.created_at,
            "updatedAt": self.updated_at
        }
    
    def sign(self):
        """Označí podpis ako podpísaný"""
        self.state = "signed"
        self.signed_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        return self
    
    def reject(self):
        """Označí podpis ako odmietnutý"""
        self.state = "rejected"
        self.updated_at = datetime.now().isoformat()
        return self
