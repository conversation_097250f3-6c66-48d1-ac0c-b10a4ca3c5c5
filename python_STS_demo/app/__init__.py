from flask import Flask, jsonify, render_template
from flask_cors import CORS
from app.config import DevelopmentConfig, TestingConfig, ProductionConfig

def create_app(config_name='default'):
    app = Flask(__name__)
    CORS(app)

    # Konfigur<PERSON>cia aplik<PERSON>cie
    if config_name == 'default':
        app.config.from_object(DevelopmentConfig)
    elif config_name == 'testing':
        app.config.from_object(TestingConfig)
    elif config_name == 'production':
        app.config.from_object(ProductionConfig)

    # Základná root cesta pre webové rozhranie
    @app.route('/')
    def index():
        return render_template('index.html')

    # API info endpoint
    @app.route('/api/info')
    def api_info():
        return jsonify({
            "message": "STS API je spustené",
            "version": "1.0.0",
            "endpoints": [
                "/api/document",
                "/api/signature"
            ]
        })

    # Registr<PERSON>cia blueprintov
    from app.routes.signature_routes import signature_bp
    from app.routes.document_routes import document_bp

    app.register_blueprint(signature_bp, url_prefix='/api')
    app.register_blueprint(document_bp, url_prefix='/api')

    # Registrácia error handlerov
    from app.utils.error_handlers import register_error_handlers
    register_error_handlers(app)

    return app
