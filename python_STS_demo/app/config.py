import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Základná konfigurácia aplikácie"""
    SECRET_KEY = os.environ.get('SECRET_KEY', 'default-secret-key')
    DEBUG = False
    TESTING = False
    API_KEYS = {
        '961657375627_7d19b177854d7c95c2a0147d42585b613bfe71cde3263bf19d37cb16131b9e43695d74907a3c346b1f708e6c1fa2df1d4fb2eb81b1df145964187c64862f81df': 'admin'
    }
    # Simulované úložisko pre dokumenty a podpisy (v produkčnom prostredí by to bola databáza)
    DOCUMENTS_STORAGE = {}
    SIGNATURES_STORAGE = {}

class DevelopmentConfig(Config):
    """Konfigurácia pre vývojové prostredie"""
    DEBUG = True

class TestingConfig(Config):
    """Konfigurácia pre testovacie prostredie"""
    TESTING = True
    DEBUG = True

class ProductionConfig(Config):
    """Konfigurácia pre produkčné prostredie"""
    DEBUG = False

# Mapovanie konfigura<PERSON>n<PERSON><PERSON> tried
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
